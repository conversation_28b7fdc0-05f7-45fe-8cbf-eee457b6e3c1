package com.zsmall.system.entity.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单确认推送ERP
 */
@Data
public class BillConfirmedSendErpDTO {
    /**
     * 渠道名称,分销固定 Distribution
     */
    private String channelName = "Distribution";
    /**
     * 店铺账号
     */
    private String thirdChannelFlag;
    /**
     * 结算编号
     */
    private String billNo;
    /**
     * 结算时间
     */
    private Date billConfirmedTime;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 合计
     */
    private BigDecimal total;
    /**
     * 是否是最后一条
     */
    private Boolean isLastData;
    /**
     * 账单明细
     */
    private BillDetailsConfirmedSendErpDTO billDetailsConfirmedSendErpDTOS;

    @Data
    public static class BillDetailsConfirmedSendErpDTO {
        /**
         * 供应商租户ID
         */
        private String supplierTenantId;
        /**
         * 供应商昵称
         */
        private String nickName;
        /**
         * 发票日期
         */
        private Date billConfirmedTime;
        /**
         * 发票号
         */
        private String billNo;
        /**
         * 分销内部订单编号
         */
        private String orderNo;
        /**
         * 类型 1发货单 2退款单
         */
        private Integer orderStatus;
        /**
         * 物流方式 PickUp自提/DropShipping代发
         */
        private String supportedLogistics;
        /**
         * 退款单编码
         */
        private String orderRefundNo;
        /**
         * 币种
         */
        private String currencyCode;
        /**
         * 产品小计
         */
        private String productSkuPrice;
        /**
         * 单价
         */
        private String unitPrice;
        /**
         * 操作费
         */
        private String operationFee;
        /**
         * 尾程派送费
         */
        private String finalDeliveryFee;
        /**
         * 商品数量
         */
        private Integer productQuantity;
        /**
         * 订单退款总金额
         */
        private BigDecimal orderRefundTotalAmount;
        /**
         * 订单总金额
         */
        private BigDecimal orderTotalAmount;
    }
}



