package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillHeadService;
import com.zsmall.system.entity.domain.bo.billHead.BillHeadBo;
import com.zsmall.system.entity.domain.bo.billHead.BillHeadExportSelectDTO;
import com.zsmall.system.entity.domain.bo.billHead.BillHeadSelectBo;
import com.zsmall.system.entity.domain.vo.billHead.BillHeadVo;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 账单-new
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/billHead")
public class BillHeadController extends BaseController {

    private final IBillHeadService billHeadService;
    private final BillHeadSupper billHeadSupper;

    /**
     * 分页查询账单列表
     */
    @GetMapping("/list")
    public R list(BillHeadSelectBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isEmpty(bo.getBillType())){
            return R.fail("账单类型不能为空");
        }
        return billHeadService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询账单详情
     */
    @GetMapping("/getBillDetailsByBillNo/{billId}/{pageNum}/{pageSize}/{orderType}")
    public R getBillDetailsByBillId(@PathVariable Long billId, @PathVariable Integer pageNum,
                                    @PathVariable Integer pageSize, @PathVariable(required = false) Integer orderType) {
        if (billId == null) {
            return R.fail("账单头ID不能为空");
        }
        if (pageSize == null) {
            return R.fail("账单头ID不能为空");
        }
        if (pageNum == null) {
            return R.fail("账单头ID不能为空");
        }
        return billHeadService.getBillDetailsByBillId(billId,pageNum,pageSize,orderType);
    }

    /**
     * 批量修改状态
     * @param billStatus 账单状态
     * @param billId 账单ID
     * @return
     */
    @PostMapping("/batchUpdateBillStatus/{billStatus}")
    public R batchUpdateBillHeadStatus(@PathVariable Integer billStatus, @RequestBody List<Long> billId) {
        if (billStatus == null) {
            return R.fail("账单状态不能为空");
        }
        return billHeadService.batchUpdateBillStatus(billStatus,billId);
    }

    /**
     * 推送账单到ERP
     * @param billNos 账单编号
     * @param orderNo 订单编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    @SaIgnore
    @GetMapping("/billConfirmedSendErp")
    public void billConfirmedSendErp(@RequestParam (value = "billNos", required = false)  String billNos,
                                     @RequestParam(value = "orderNo", required = false) String orderNo,
                                     @RequestParam(value = "startTime", required = false) String startTime,
                                     @RequestParam(value = "endTime", required = false) String endTime) {
        ThreadUtil.execAsync(()-> {
            try{
                billHeadSupper.sendBillToErpByConfirmed(startTime,endTime,billNos,orderNo);
            }catch (Exception e){
                log.error(String.valueOf(e));
            }
        });
    }

    /**
     * 导出分销商账单信息
     * @param billHeadExportSelectDTO
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportTenantBill")
    public R exportTenantBill( @RequestBody BillHeadExportSelectDTO billHeadExportSelectDTO, HttpServletResponse response) throws IOException {
        if (billHeadExportSelectDTO.getBillType()==null){
            throw new RuntimeException("账单业务类型不能为空");
        }
        try {
            billHeadService.exportTenantBill(billHeadExportSelectDTO,response);
            return R.ok("导出成功");
        }catch (Exception e){
            return R.fail("导出异常");
        }

    }

    /**
     * 导出供应商账单信息
     * @param billHeadExportSelectDTO
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportSupperTenantBill")
    public R exportSupperTenantBill( @RequestBody BillHeadExportSelectDTO billHeadExportSelectDTO, HttpServletResponse response) throws IOException {
        //供应商筛选条件只有 账单编号/供应商
        if (billHeadExportSelectDTO.getBillType()==null){
            throw new RuntimeException("账单业务类型不能为空");
        }
        billHeadService.exportSupperTenantBill(billHeadExportSelectDTO,response);
        return R.ok();
    }




    /**
     * 获取账单-new详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:head:query")
    @GetMapping("/{id}")
    public R<BillHeadVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(billHeadService.queryById(id));
    }

    /**
     * 新增账单-new
     */
    @SaCheckPermission("system:head:add")
    @Log(title = "账单-new", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BillHeadBo bo) {
        return toAjax(billHeadService.insertByBo(bo));
    }

    /**
     * 修改账单-new
     */
    @SaCheckPermission("system:head:edit")
    @Log(title = "账单-new", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BillHeadBo bo) {
        return toAjax(billHeadService.updateByBo(bo));
    }

    /**
     * 删除账单-new
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:head:remove")
    @Log(title = "账单-new", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(billHeadService.deleteWithValidByIds(List.of(ids), true));
    }
}
