package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.thread.ThreadUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.IBillDetailsRepairService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * 账单明细修补表控制器
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/billDetailsRepair")
public class BillDetailsRepairController extends BaseController {

    private final IBillDetailsRepairService billDetailsRepairService;

    /**
     * 修补账单数据
     * @param billNo 账单编号（必填，单个账单编号）
     * @param orderNo 订单编号（必填，多个订单号用逗号拼接）
     */
    @SaIgnore
    @PostMapping("/repairBillData")
    @Log(title = "账单数据修补", businessType = BusinessType.UPDATE)
    public R<String> repairBillData(@RequestParam(value = "billNo", required = true) @NotBlank(message = "账单编号不能为空") String billNo,
                                    @RequestParam(value = "orderNo", required = true) @NotBlank(message = "订单编号不能为空") String orderNo) {

        log.info("开始修补账单数据， 账单编号：{}, 订单编号：{}", billNo, orderNo);
        try {
            billDetailsRepairService.repairBillData(billNo, orderNo);
        } catch (Exception e) {
            log.error("账单数据修补异常： 账单编号={}, 订单编号={}, 错误信息={}",  billNo, orderNo, e.getMessage(), e);
        }
        return R.ok("账单数据修补任务已启动，请稍后查看处理结果");
    }


    /**
     * 重新推送差值数据到ERP
     */
    @PostMapping("/resendDifferenceToErp")
    @Log(title = "重新推送差值数据到ERP", businessType = BusinessType.UPDATE)
    public R<String> resendDifferenceToErp(@RequestParam("billNo") @NotBlank(message = "账单编号不能为空") String billNo) {

        ThreadUtil.execAsync(() -> {
            try {
                billDetailsRepairService.resendDifferenceToErp(billNo);
            } catch (Exception e) {
                log.error("重新推送差值数据到ERP异常：账单编号={}, 错误信息={}", billNo, e.getMessage(), e);
            }
        });

        return R.ok("差值数据重新推送任务已启动");
    }

}
