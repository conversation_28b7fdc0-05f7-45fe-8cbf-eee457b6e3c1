# 账单数据修补推送ERP需求文档

## 1. 需求背景

在分销系统运行过程中，由于数据同步异常、业务规则调整、系统bug修复等原因，可能导致已生成的账单数据存在错误或不准确的情况。为了保证账单数据的准确性和与ERP系统的数据一致性，需要提供账单数据修补功能，并将修补前后的差值数据推送到ERP系统。

## 2. 功能概述

### 2.1 核心功能
- 支持按订单号、账单号、时间范围等条件进行账单数据修补
- 备份修补前的原始账单数据到修补表
- 重新生成指定租户指定时间范围的账单数据
- 计算修补前后的差值数据
- 将差值数据推送到ERP系统
- 支持同一订单的多次修补操作
- 提供定时任务推送未推送的修补数据

### 2.2 业务价值
- 确保账单数据的准确性
- 保持分销系统与ERP系统的数据一致性
- 提供完整的修补历史记录和审计追踪
- 支持灵活的修补条件和批量处理

## 3. 详细需求

### 3.1 数据结构设计

#### 3.1.1 账单明细修补表 (bill_details_repair)
- **表名**: `bill_details_repair`
- **用途**: 备份修补前的原始账单数据，用于差值计算和历史追踪
- **字段结构**: 与 `bill_details` 表完全一致，包含以下关键字段：
  - 所有业务字段（订单信息、金额信息、商品信息等）
  - `is_send_erp`: 修补差值数据是否推送ERP (0-未推送, 1-已推送)
  - `send_erp_time`: 修补差值数据推送ERP时间

#### 3.1.2 字段维度说明
- `bill_details.is_send_erp`: 表示原始账单数据的ERP推送状态
- `bill_details_repair.is_send_erp`: 表示修补差值数据的ERP推送状态
- 两个字段业务含义不同，在数据复制时需要独立处理

### 3.2 接口设计

#### 3.2.1 账单数据修补接口
- **接口路径**: `POST /system/billDetailsRepair/repairBillData`
- **请求参数**:
  - `tenantId` (必填): 租户ID
  - `billNo` (必填): 账单编号，单个账单编号
  - `orderNo` (必填): 订单编号，多个订单号用逗号分隔
  - `startTime` (可选): 开始时间，用于时间范围限制
  - `endTime` (可选): 结束时间，用于时间范围限制
- **参数校验**: 租户ID、账单编号、订单编号均为必填项
- **执行方式**: 异步执行，立即返回任务启动确认
- **业务逻辑**: 针对单个账单下的指定订单进行精确修补，确保操作的准确性和安全性

#### 3.2.2 查询修补记录接口
- **分页查询**: `GET /system/billDetailsRepair/list`
- **按订单号查询**: `GET /system/billDetailsRepair/getByOrderNos`
- **按租户和时间范围查询**: `GET /system/billDetailsRepair/getByTenantAndTimeRange`

#### 3.2.3 差值计算接口
- **查看差值**: `GET /system/billDetailsRepair/getDifferenceCalculation`
- **重新推送**: `POST /system/billDetailsRepair/resendDifferenceToErp`

### 3.3 业务流程

#### 3.3.1 账单修补主流程
1. **参数校验**: 验证租户ID和查询条件的有效性
2. **清理旧记录**:
   - 查询当前要修补的订单对应的账单编号
   - 按"账单编号+订单号"组合查询未推送修补记录
   - 删除这些旧记录，避免重复推送风险
   - 保留已推送的历史记录用于审计
   - 确保不同账单下的相同订单号独立处理
3. **数据备份**:
   - 根据查询条件查询需要修补的账单明细数据
   - 将原始数据复制到 `bill_details_repair` 表
   - 复制时忽略ERP推送相关字段，保持字段维度独立
   - 设置修补数据的推送状态为未推送
3. **确定修补范围**: 
   - 获取当前月的时间范围
   - 确保修补操作在合理的时间范围内
4. **删除原数据**:
   - 删除当前租户当前月的 `bill_head` 数据
   - 删除当前租户当前月的 `bill_details` 数据
   - 删除当前租户当前月的 `bill_abstract_detail` 数据
5. **重新生成数据**:
   - 调用 `generatedDistributorBillByTenant` 重新生成账单数据
   - 调用 `generatedTransactionReceipt` 生成账单汇总数据
6. **差值计算**:
   - 对比修补表中的数据（修补前）和当前账单表中的数据（修补后）
   - 计算各金额字段的差值
   - 只保留有差值的记录
7. **ERP推送**:
   - 将差值数据转换为ERP推送格式
   - 推送到 `bill.repair.queue` 队列
   - 更新修补表中的推送状态

#### 3.3.2 多次修补支持
- **精确清理逻辑**: 新修补操作前，按"账单编号+订单号"组合清理旧的未推送修补记录
- **避免重复推送**: 确保同一账单下的同一订单只有最新的修补数据会被推送到ERP
- **独立账单处理**: 不同账单编号下的相同订单号独立处理，互不影响
- **历史记录管理**: 已推送的修补记录会被保留作为历史审计
- **数据一致性**: 保证修补表中每个"账单编号+订单号"组合只有一条未推送记录

#### 3.3.3 定时任务推送
- **任务名称**: `sendBillRepairToErp`
- **执行频率**: 每月5号执行
- **执行逻辑**:
  - 查询所有 `is_send_erp = 0` 的未推送修补数据
  - 按租户分组进行批量推送
  - 推送成功后更新 `is_send_erp = 1` 和 `send_erp_time`
  - 支持异常处理，单个租户失败不影响其他租户

### 3.4 差值计算逻辑

#### 3.4.1 计算字段
- **操作费差值**: `afterOperationFee - beforeOperationFee`
- **尾程派送费差值**: `afterFinalDeliveryFee - beforeFinalDeliveryFee`
- **产品小计差值**: `afterProductSkuPrice - beforeProductSkuPrice`
- **订单金额差值**: `afterOrderTotalAmount - beforeOrderTotalAmount`
- **退款金额差值**: `afterOrderRefundTotalAmount - beforeOrderRefundTotalAmount`

#### 3.4.2 差值判断
- 只有当至少一个字段存在差值（不等于0）时，才认为有差值
- 支持null值的安全处理，null值按0处理

### 3.5 ERP推送设计

#### 3.5.1 推送格式
- **统一DTO**: 使用现有的 `BillConfirmedSendErpDTO` 结构
- **标识字段**: `channelName = "Distribution_Repair"` 标识为修补数据
- **差值映射**: 将差值数据映射到现有字段结构中

#### 3.5.2 推送机制
- **队列名称**: `bill.repair.queue`
- **推送方式**: 异步推送，通过RabbitMQ队列
- **批量处理**: 按差值记录逐个推送，支持批量标识

## 4. 技术实现

### 4.1 核心类设计
- **Controller**: `BillDetailsRepairController` - 提供REST接口
- **Service**: `IBillDetailsRepairService` 和 `BillDetailsRepairServiceImpl` - 业务逻辑实现
- **Entity**: `BillDetailsRepair` - 修补表实体类
- **Mapper**: `BillDetailsRepairMapper` - 数据访问层
- **DTO**: `BillRepairDifferenceDTO` - 差值计算结果

### 4.2 关键技术点
- **事务控制**: 使用 `@Transactional` 确保数据一致性
- **异步处理**: 使用 `ThreadUtil.execAsync` 避免接口超时
- **租户隔离**: 使用 `TenantHelper.ignore` 处理多租户场景
- **分页查询**: 支持大数据量的分页处理
- **异常处理**: 完善的异常处理和日志记录

### 4.3 数据库设计
- **索引优化**: 在订单号、租户ID、创建时间等字段上建立索引
- **查询优化**: 使用ROW_NUMBER()窗口函数获取最新记录
- **批量操作**: 支持批量插入和批量更新操作

## 5. 风险控制

### 5.1 数据安全
- **备份机制**: 修补前强制备份原始数据
- **事务回滚**: 异常情况下自动回滚所有操作
- **权限控制**: 接口需要适当的权限验证

### 5.2 性能考虑
- **异步执行**: 避免长时间阻塞用户请求
- **分批处理**: 大数据量时分批处理，避免内存溢出
- **索引优化**: 确保查询性能

### 5.3 业务风险
- **时间范围限制**: 只允许修补当前月的数据
- **重复修补检测**: 支持同一订单的多次修补
- **差值验证**: 确保差值计算的准确性

## 6. 验收标准

### 6.1 功能验收
- [ ] 支持按各种条件进行账单修补
- [ ] 正确备份和恢复账单数据
- [ ] 准确计算修补前后差值
- [ ] 成功推送差值数据到ERP
- [ ] 支持多次修补同一订单
- [ ] 定时任务正常执行

### 6.2 性能验收
- [ ] 接口响应时间 < 3秒（异步启动）
- [ ] 支持处理10000+订单的批量修补
- [ ] 数据库查询性能优化

### 6.3 稳定性验收
- [ ] 异常情况下数据不丢失
- [ ] 支持并发修补操作
- [ ] 完善的日志记录和监控

## 7. 上线计划

### 7.1 开发阶段
- [ ] 数据库表结构创建
- [ ] 核心业务逻辑开发
- [ ] 单元测试和集成测试

### 7.2 测试阶段
- [ ] 功能测试
- [ ] 性能测试
- [ ] 异常场景测试

### 7.3 上线阶段
- [ ] 生产环境部署
- [ ] 数据迁移和验证
- [ ] 监控和告警配置
